import { useState } from 'react';
import { useGoogleConnection } from '@/hooks/useGoogleConnection';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import {
  Link as LinkIcon,
  Unlink,
  RefreshCw,
  User,
  MoreHorizontal
} from 'lucide-react';

interface GoogleAccountConnectionProps {
  className?: string;
}

export function GoogleAccountConnection({ className }: GoogleAccountConnectionProps) {
  const { 
    isConnected, 
    googleAccount, 
    isLoading, 
    error, 
    connect, 
    disconnect, 
    switchAccount 
  } = useGoogleConnection();

  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const handleConnect = async () => {
    setActionLoading('connect');
    await connect();
    setActionLoading(null);
  };

  const handleDisconnect = async () => {
    setActionLoading('disconnect');
    await disconnect();
    setActionLoading(null);
  };

  const handleSwitchAccount = async () => {
    setActionLoading('switch');
    await switchAccount();
    setActionLoading(null);
  };

  if (isLoading && !actionLoading) {
    return (
      <div className={`flex items-center gap-3 px-3 py-2 ${className}`}>
        <div className="w-4 h-4 rounded-full bg-background-hover animate-pulse flex-shrink-0" />
        <div className="flex flex-col gap-1 min-w-0 flex-1">
          <div className="h-3 bg-background-hover rounded animate-pulse w-24" />
          <div className="h-2 bg-background-hover rounded animate-pulse w-32" />
        </div>
      </div>
    );
  }

  if (!isConnected) {
    return (
      <div className={`flex items-center justify-between gap-3 px-3 py-2 ${className}`}>
        <div className="flex items-center gap-3 min-w-0 flex-1">
          <div className="w-4 h-4 rounded-full flex items-center justify-center flex-shrink-0 bg-background-hover">
            <svg
              className="w-3 h-3 text-text-muted"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
          </div>
          <div className="flex flex-col min-w-0 flex-1">
            <span className="text-xs font-medium text-text-secondary">Google Account</span>
            <span className="text-xs text-text-muted">Not connected</span>
          </div>
        </div>
        <Button
          size="sm"
          variant="outline"
          onClick={handleConnect}
          disabled={actionLoading === 'connect'}
          className="text-xs px-2 py-1 h-6"
        >
          {actionLoading === 'connect' ? (
            <RefreshCw className="h-3 w-3 animate-spin" />
          ) : (
            <>
              <LinkIcon className="h-3 w-3 mr-1" />
              Connect
            </>
          )}
        </Button>
      </div>
    );
  }

  return (
    <div className={`flex items-center justify-between gap-3 px-3 py-2 ${className}`}>
      <div className="flex items-center gap-3 min-w-0 flex-1">
        <div className="w-4 h-4 rounded-full flex items-center justify-center flex-shrink-0 bg-green-600">
          {googleAccount?.picture ? (
            <img
              src={googleAccount.picture}
              alt={googleAccount.name}
              className="w-full h-full rounded-full object-cover"
            />
          ) : (
            <User className="h-2.5 w-2.5 text-white" />
          )}
        </div>
        <div className="flex flex-col min-w-0 flex-1">
          <span className="text-xs font-medium text-text-secondary truncate">
            {googleAccount?.name || 'Google Account'}
          </span>
          <span className="text-xs text-text-muted truncate">
            {googleAccount?.email || 'Connected'}
          </span>
        </div>
      </div>
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            size="sm"
            variant="ghost"
            className="h-6 w-6 p-0 text-text-muted hover:text-text-secondary"
            disabled={!!actionLoading}
          >
            {actionLoading ? (
              <RefreshCw className="h-3 w-3 animate-spin" />
            ) : (
              <MoreHorizontal className="h-3 w-3" />
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuItem 
            onClick={handleSwitchAccount}
            disabled={actionLoading === 'switch'}
            className="text-xs"
          >
            <RefreshCw className="h-3 w-3 mr-2" />
            Switch Account
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem 
            onClick={handleDisconnect}
            disabled={actionLoading === 'disconnect'}
            className="text-xs text-red-400 focus:text-red-300"
          >
            <Unlink className="h-3 w-3 mr-2" />
            Disconnect
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

export default GoogleAccountConnection;
